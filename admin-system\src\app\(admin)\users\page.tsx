"use client"

import { useState, useEffect, useCallback } from 'react';
import { withAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { apiCall, LoadingManager } from '@/lib/api-helpers';

// Import our new components
import { UserStatsCards } from '@/components/users/user-stats-cards';
import { UserFilters } from '@/components/users/user-filters';
import { UserTable } from '@/components/users/user-table';
import { UserPagination } from '@/components/users/user-pagination';
import { BanUserDialog, SetVipDialog } from '@/components/users/user-action-dialogs';
import { UserActivationHistory } from '@/components/users/user-activation-history';
import { DeleteUserDialog } from '@/components/users/delete-user-dialog';

// Import services and types
import { UserService, UserListParams, UserStats } from '@/services/user.service';
import { User } from '@/services/auth.service';

function UsersPage() {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats>({
    total: 0,
    active: 0,
    banned: 0,
    vip: 0,
    newThisMonth: 0,
  });
  // Loading states are now managed by LoadingManager
  const [loadingStates, setLoadingStates] = useState(LoadingManager.getState());

  // Subscribe to loading state changes
  useEffect(() => {
    const unsubscribe = LoadingManager.subscribe(setLoadingStates);
    return unsubscribe;
  }, []);

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // Filter state
  const [filters, setFilters] = useState<UserListParams>({
    page: 1,
    limit: 20,
  });

  // Dialog state
  const [banDialog, setBanDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  const [vipDialog, setVipDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  const [activationHistoryDialog, setActivationHistoryDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  // Load users data
  const loadUsers = useCallback(async () => {
    const result = await apiCall(
      () => UserService.getUsers(filters),
      {
        loadingKey: 'loadUsers',
        showErrorToast: true,
        errorMessage: '加载用户列表失败'
      }
    );

    if (result.success && result.data) {
      setUsers(result.data.users);
      setPagination({
        page: result.data.pagination?.page || 1,
        limit: result.data.pagination?.limit || 20,
        total: result.data.pagination?.total || 0,
        totalPages: result.data.pagination?.totalPages || 0,
      });
    }
  }, [filters]);

  // Load user statistics
  const loadStats = useCallback(async () => {
    const result = await apiCall(
      () => UserService.getUserStats(),
      {
        loadingKey: 'loadStats',
        showErrorToast: true,
        errorMessage: '加载统计数据失败'
      }
    );

    if (result.success && result.data) {
      setStats(result.data);
    }
  }, []);

  // Event handlers
  const handleFiltersChange = (newFilters: UserListParams) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setFilters({ page: 1, limit: 20 });
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleRefresh = () => {
    loadUsers();
    loadStats();
  };

  // User action handlers
  const handleBanUser = async (reason: string, expiresAt?: Date) => {
    if (!banDialog.user) return;

    const result = await apiCall(
      () => UserService.banUser(banDialog.user!.id, {
        reason,
        expiresAt: expiresAt?.toISOString(),
      }),
      {
        loadingKey: 'banUser',
        showSuccessToast: true,
        successMessage: '用户已封禁',
        showErrorToast: true,
        errorMessage: '封禁用户失败'
      }
    );

    if (result.success) {
      setBanDialog({ open: false, user: null });
      loadUsers();
      loadStats();
    }
  };

  const handleUnbanUser = async (user: User) => {
    const result = await apiCall(
      () => UserService.unbanUser(user.id),
      {
        loadingKey: 'unbanUser',
        showSuccessToast: true,
        successMessage: '用户已解封',
        showErrorToast: true,
        errorMessage: '解封用户失败'
      }
    );

    if (result.success) {
      loadUsers();
      loadStats();
    }
  };

  const handleSetVip = async (level: string, expiresAt?: Date) => {
    if (!vipDialog.user) return;

    const result = await apiCall(
      () => UserService.setVipLevel(vipDialog.user!.id, {
        level: level as any,
        expiresAt: expiresAt?.toISOString(),
      }),
      {
        loadingKey: 'setVip',
        showSuccessToast: true,
        successMessage: 'VIP等级已设置',
        showErrorToast: true,
        errorMessage: '设置VIP等级失败'
      }
    );

    if (result.success) {
      setVipDialog({ open: false, user: null });
      loadUsers();
      loadStats();
    }
  };

  const handleViewUser = (_user: User) => {
    // TODO: Implement user detail view
    toast.info('用户详情功能开发中');
  };

  const handleEditUser = (_user: User) => {
    // TODO: Implement user edit
    toast.info('编辑用户功能开发中');
  };

  const handleDeleteUser = async (user: User) => {
    const result = await apiCall(
      () => UserService.deleteUser(user.id),
      {
        loadingKey: 'deleteUser',
        showSuccessToast: true,
        successMessage: '用户已删除',
        showErrorToast: true,
        errorMessage: '删除用户失败'
      }
    );

    if (result.success) {
      setDeleteDialog({ open: false, user: null });
      loadUsers();
      loadStats();
    }
  };

  const handleViewActivationHistory = (user: User) => {
    setActivationHistoryDialog({ open: true, user });
  };

  // Load data on mount and when filters change
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">用户管理</h2>
          <p className="text-muted-foreground">
            管理uniapp前端注册用户，包括搜索、封号解封、VIP等级管理等功能
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loadingStates.loadUsers || loadingStates.loadStats}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${(loadingStates.loadUsers || loadingStates.loadStats) ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            添加用户
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <UserStatsCards stats={stats} loading={loadingStates.loadStats} />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            管理所有注册用户，支持搜索、筛选和批量操作
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <UserFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onReset={handleFiltersReset}
          />

          {/* User Table */}
          <UserTable
            users={users}
            loading={loadingStates.loadUsers}
            onBanUser={(user) => setBanDialog({ open: true, user })}
            onUnbanUser={handleUnbanUser}
            onSetVip={(user) => setVipDialog({ open: true, user })}
            onViewUser={handleViewUser}
            onEditUser={handleEditUser}
            onViewActivationHistory={handleViewActivationHistory}
            onDeleteUser={(user) => setDeleteDialog({ open: true, user })}
          />

          {/* Pagination */}
          {pagination.total > 0 && (
            <UserPagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              pageSize={pagination.limit}
              total={pagination.total}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <BanUserDialog
        open={banDialog.open}
        onOpenChange={(open) => setBanDialog({ open, user: banDialog.user })}
        user={banDialog.user}
        onConfirm={handleBanUser}
        loading={loadingStates.banUser}
      />

      <SetVipDialog
        open={vipDialog.open}
        onOpenChange={(open) => setVipDialog({ open, user: vipDialog.user })}
        user={vipDialog.user}
        onConfirm={handleSetVip}
        loading={loadingStates.setVip}
      />

      <UserActivationHistory
        open={activationHistoryDialog.open}
        onOpenChange={(open) => setActivationHistoryDialog({ open, user: activationHistoryDialog.user })}
        user={activationHistoryDialog.user}
      />

      {/* Delete User Confirmation Dialog */}
      <DeleteUserDialog
        open={deleteDialog.open}
        user={deleteDialog.user}
        onClose={() => setDeleteDialog({ open: false, user: null })}
        onConfirm={handleDeleteUser}
      />
    </div>
  );
}

export default withAuth(UsersPage);
