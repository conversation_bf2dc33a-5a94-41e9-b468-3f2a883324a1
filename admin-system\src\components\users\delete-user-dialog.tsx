"use client"

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';
import { User } from '@/services/user.service';

interface DeleteUserDialogProps {
  open: boolean;
  user: User | null;
  onClose: () => void;
  onConfirm: (user: User) => void;
}

export function DeleteUserDialog({
  open,
  user,
  onClose,
  onConfirm
}: DeleteUserDialogProps) {
  const [confirmText, setConfirmText] = useState('');
  const expectedText = user?.username || '';

  const handleConfirm = () => {
    if (user && confirmText === expectedText) {
      onConfirm(user);
      handleClose();
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onClose();
  };

  const isConfirmValid = confirmText === expectedText;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            删除用户确认
          </DialogTitle>
          <DialogDescription>
            此操作将永久删除用户及其所有相关数据，包括：
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">将被删除的数据：</h4>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• 用户账户信息</li>
              <li>• 登录会话记录</li>
              <li>• 操作审计日志</li>
              <li>• VIP等级和权限</li>
              <li>• 其他关联数据</li>
            </ul>
          </div>

          {user && (
            <div className="bg-gray-50 border rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">用户信息：</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><strong>用户名：</strong> {user.username}</div>
                <div><strong>姓名：</strong> {user.full_name || '未设置'}</div>
                <div><strong>角色：</strong> {user.role}</div>
                <div><strong>VIP等级：</strong> {user.vip_level}</div>
                <div><strong>注册来源：</strong> {user.registration_source}</div>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="confirm-text" className="text-sm font-medium">
              请输入用户名 <code className="bg-gray-100 px-1 rounded">{expectedText}</code> 来确认删除：
            </Label>
            <Input
              id="confirm-text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={`请输入 ${expectedText}`}
              className={confirmText && !isConfirmValid ? 'border-red-300' : ''}
            />
            {confirmText && !isConfirmValid && (
              <p className="text-sm text-red-600">用户名不匹配</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!isConfirmValid}
          >
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
