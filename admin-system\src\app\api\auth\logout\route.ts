import { NextRequest } from 'next/server';
import { SessionManager } from '@/lib/auth';
import {
  withAuth,
  successResponse,
  errorResponse,
  logAuditEvent,
  getClientIP
} from '@/lib/api-utils';

export const POST = withAuth(async (request: NextRequest, { user, session }) => {
  try {
    const clientIP = getClientIP(request);

    // 删除当前会话
    if (session?.id) {
      await SessionManager.deleteSession(session.id);
    }

    // 记录登出事件
    await logAuditEvent({
      userId: user.id,
      action: 'LOGOUT',
      details: {
        sessionId: session?.id,
        username: user.username
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return successResponse(null, '登出成功');

  } catch (error) {
    console.error('Logout error:', error);
    return errorResponse('登出失败', 500, 'LOGOUT_FAILED');
  }
});
