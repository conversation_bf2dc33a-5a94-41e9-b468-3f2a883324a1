"use client"

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';
import { ActivationCode, VIP_LEVEL_CONFIG } from '@/services/activation-code.service';

interface DeleteActivationCodeDialogProps {
  open: boolean;
  code: ActivationCode | null;
  onClose: () => void;
  onConfirm: (code: ActivationCode) => void;
}

export function DeleteActivationCodeDialog({
  open,
  code,
  onClose,
  onConfirm
}: DeleteActivationCodeDialogProps) {
  const [confirmText, setConfirmText] = useState('');
  const expectedText = 'DELETE';

  const handleConfirm = () => {
    if (code && confirmText === expectedText) {
      onConfirm(code);
      handleClose();
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onClose();
  };

  const isConfirmValid = confirmText === expectedText;

  const formatCode = (codeStr: string) => {
    return codeStr.replace(/(.{4})/g, '$1-').slice(0, -1);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            删除激活码确认
          </DialogTitle>
          <DialogDescription>
            此操作将永久删除激活码，无法恢复。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">注意事项：</h4>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• 激活码将被永久删除</li>
              <li>• 已使用的激活码不能删除</li>
              <li>• 删除后无法恢复</li>
              <li>• 相关的使用记录将保留</li>
            </ul>
          </div>

          {code && (
            <div className="bg-gray-50 border rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">激活码信息：</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><strong>激活码：</strong> <code className="bg-gray-100 px-1 rounded">{formatCode(code.code)}</code></div>
                <div><strong>VIP等级：</strong> {VIP_LEVEL_CONFIG[code.vip_level]?.label}</div>
                <div><strong>有效期：</strong> {code.vip_duration_days} 天</div>
                <div><strong>状态：</strong> 
                  <span className={`ml-1 px-2 py-1 rounded text-xs ${
                    code.status === 'active' ? 'bg-green-100 text-green-800' :
                    code.status === 'used' ? 'bg-blue-100 text-blue-800' :
                    code.status === 'expired' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {code.status === 'active' ? '可用' :
                     code.status === 'used' ? '已使用' :
                     code.status === 'expired' ? '已过期' : '已禁用'}
                  </span>
                </div>
                {code.description && (
                  <div><strong>描述：</strong> {code.description}</div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="confirm-text" className="text-sm font-medium">
              请输入 <code className="bg-gray-100 px-1 rounded">{expectedText}</code> 来确认删除：
            </Label>
            <Input
              id="confirm-text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value.toUpperCase())}
              placeholder={`请输入 ${expectedText}`}
              className={confirmText && !isConfirmValid ? 'border-red-300' : ''}
            />
            {confirmText && !isConfirmValid && (
              <p className="text-sm text-red-600">确认文本不匹配</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!isConfirmValid}
          >
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
