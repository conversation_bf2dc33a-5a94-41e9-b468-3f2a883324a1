import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import {
  withAuth,
  successResponse,
  errorResponse,
  validateRequest,
  validators,
  logAuditEvent,
  getClientIP
} from '@/lib/api-utils';
import bcrypt from 'bcryptjs';

export const POST = withAuth(async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const clientIP = getClientIP(request);

    // 验证请求数据
    const { isValid, errors, data } = validateRequest<{
      currentPassword: string;
      newPassword: string;
      confirmPassword: string;
    }>(body, {
      currentPassword: validators.required,
      newPassword: [
        validators.required,
        validators.minLength(8),
        validators.password
      ],
      confirmPassword: validators.required,
    });

    if (!isValid) {
      const firstError = Object.values(errors)[0];
      return errorResponse(firstError || '验证失败', 400, 'VALIDATION_ERROR');
    }

    // 验证新密码和确认密码是否一致
    if (data.newPassword !== data.confirmPassword) {
      return errorResponse('新密码和确认密码不一致', 400, 'PASSWORD_MISMATCH');
    }

    // 获取当前用户的完整信息
    const currentUser = await UserManager.getUserById(user.userId);
    if (!currentUser) {
      return errorResponse('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(data.currentPassword, currentUser.password_hash);
    if (!isCurrentPasswordValid) {
      // 记录密码验证失败的审计日志
      await logAuditEvent({
        userId: user.userId,
        action: 'PASSWORD_CHANGE_FAILED',
        details: {
          reason: 'Invalid current password',
          username: currentUser.username
        },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || undefined,
      });

      return errorResponse('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await bcrypt.compare(data.newPassword, currentUser.password_hash);
    if (isSamePassword) {
      return errorResponse('新密码不能与当前密码相同', 400, 'SAME_PASSWORD');
    }

    // 生成新密码的哈希
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(data.newPassword, saltRounds);

    // 更新密码
    await UserManager.updateUserPassword(user.userId, newPasswordHash);

    // 记录密码修改成功的审计日志
    await logAuditEvent({
      userId: user.userId,
      action: 'PASSWORD_CHANGED',
      details: {
        username: currentUser.username,
        changedAt: new Date().toISOString()
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    // 清除用户的所有会话（强制重新登录）
    await UserManager.clearUserSessions(user.userId);

    return successResponse({
      message: '密码修改成功，请重新登录'
    });

  } catch (error) {
    console.error('Change password error:', error);
    return errorResponse('密码修改失败', 500, 'CHANGE_PASSWORD_FAILED');
  }
});
